# نظام إدارة الموظفين - Employee Management System

تطبيق Flutter لإدارة الموظفين مع دعم تسجيل الدخول عبر API أو البيانات التجريبية.

## الميزات الرئيسية

- 🔐 **نظام تسجيل دخول متقدم**: دعم API الحقيقي والبيانات التجريبية
- 📊 **لوحة تحكم شاملة**: عرض إحصائيات الموظفين والإجازات
- 👤 **إدارة الملف الشخصي**: عرض وتحديث بيانات الموظف
- 📅 **نظام الإجازات**: طلب إجازات وعرض الأرصدة
- 🎨 **واجهة عربية**: دعم كامل للغة العربية مع تصميم Material Design

## إعداد المشروع

### المتطلبات
- Flutter SDK (3.8.1 أو أحدث)
- Dart SDK
- Android Studio أو VS Code

### التثبيت
```bash
# استنساخ المشروع
git clone [repository-url]
cd Mobile

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

## تكوين API

### إعداد الخادم
1. افتح ملف `lib/config/api_config.dart`
2. قم بتغيير `baseUrl` إلى رابط الخادم الخاص بك:
```dart
static const String baseUrl = 'http://your-server.com:8069';
```

### استخدام API
1. في شاشة تسجيل الدخول، فعّل "استخدام API الحقيقي"
2. أدخل اسم قاعدة البيانات
3. أدخل اسم المستخدم وكلمة المرور

## البيانات التجريبية

للاختبار السريع، يمكنك استخدام البيانات التجريبية:

**أسماء المستخدمين المتاحة:**
- `admin` - كلمة المرور: `admin123`
- `ahmed.ali` - كلمة المرور: `password123`
- `sara.mohamed` - كلمة المرور: `password123`
- `omar.hassan` - كلمة المرور: `password123`

## هيكل المشروع

```
lib/
├── config/          # ملفات التكوين
├── models/          # نماذج البيانات
├── providers/       # إدارة الحالة (Provider)
├── screens/         # شاشات التطبيق
├── services/        # خدمات API والبيانات
├── utils/           # أدوات مساعدة
└── widgets/         # مكونات واجهة المستخدم
```

## API المدعوم

التطبيق يدعم API بالتنسيق التالي:

```json
POST /api/login
{
  "jsonrpc": "2.0",
  "method": "call",
  "params": {
    "db": "database_name",
    "login": "username",
    "password": "password"
  },
  "id": null
}
```

**الاستجابة المتوقعة:**
```json
{
  "jsonrpc": "2.0",
  "result": {
    "success": true,
    "session_id": "session_id",
    "user_id": 123,
    "message": "تم تسجيل الدخول بنجاح"
  },
  "id": null
}
```
