import 'package:flutter_test/flutter_test.dart';
import 'package:bsic_bank/services/api_service.dart';
import 'package:bsic_bank/services/auth_service.dart';
import 'package:bsic_bank/models/employee.dart';

void main() {
  group('API Integration Tests', () {
    test('Employee model should handle API response correctly', () {
      // Mock API response data
      final apiResponse = {
        'int_id': '123',
        'name': 'أحمد محمد',
        'department_id': [1, 'قسم تقنية المعلومات'],
        'parent_id': [2, 'سارة أحمد'],
        'coach_id': [3, 'محمد علي'],
        'connected_with_comp': true,
        'national_number': '**********',
      };

      // Create employee from API response
      final employee = Employee.fromApiResponse(apiResponse);

      // Verify the data is correctly mapped
      expect(employee.id, '123');
      expect(employee.name, 'أحمد محمد');
      expect(employee.intId, '123');
      expect(employee.departmentName, 'قسم تقنية المعلومات');
      expect(employee.managerName, 'سارة أحمد');
      expect(employee.coachName, 'محمد علي');
      expect(employee.connectedWithComp, true);
      expect(employee.nationalNumber, '**********');
    });

    test('Employee model should handle missing fields gracefully', () {
      // Mock API response with minimal data
      final apiResponse = {
        'int_id': '456',
        'name': 'فاطمة أحمد',
      };

      // Create employee from API response
      final employee = Employee.fromApiResponse(apiResponse);

      // Verify the data is correctly mapped with defaults
      expect(employee.id, '456');
      expect(employee.name, 'فاطمة أحمد');
      expect(employee.intId, '456');
      expect(employee.department, '');
      expect(employee.manager, '');
      expect(employee.coachName, '');
      expect(employee.connectedWithComp, null);
      expect(employee.nationalNumber, null);
    });

    test('Employee model should handle array fields correctly', () {
      // Mock API response with array fields
      final apiResponse = {
        'int_id': '789',
        'name': 'عمر حسن',
        'department_id': [5, 'الموارد البشرية'],
        'parent_id': [6, 'ليلى محمد'],
        'coach_id': null, // Test null coach
        'connected_with_comp': false,
        'national_number': 9876543210,
      };

      // Create employee from API response
      final employee = Employee.fromApiResponse(apiResponse);

      // Verify the data is correctly mapped
      expect(employee.id, '789');
      expect(employee.name, 'عمر حسن');
      expect(employee.departmentName, 'الموارد البشرية');
      expect(employee.managerName, 'ليلى محمد');
      expect(employee.coachName, ''); // Should be empty when coach_id is null
      expect(employee.connectedWithComp, false);
      expect(employee.nationalNumber, '9876543210');
    });
  });

  group('AuthService Tests', () {
    test('should return available usernames for mock data', () {
      final authService = AuthService();
      final usernames = authService.getAvailableUsernames();
      
      expect(usernames, isNotEmpty);
      expect(usernames, contains('admin'));
      expect(usernames, contains('ahmed.ali'));
    });
  });
}
