import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../models/leave_request.dart';
import '../utils/app_colors.dart';

class LeaveHistoryCard extends StatelessWidget {
  final LeaveRequest leaveRequest;

  const LeaveHistoryCard({
    super.key,
    required this.leaveRequest,
  });

  @override
  Widget build(BuildContext context) {
    Color statusColor;
    IconData statusIcon;
    String statusText;
    Color cardBorderColor;

    switch (leaveRequest.status) {
      case LeaveStatus.approved:
        statusColor = AppColors.success;
        statusIcon = FontAwesomeIcons.circleCheck;
        statusText = 'موافق عليها';
        cardBorderColor = AppColors.success.withOpacity(0.3);
        break;
      case LeaveStatus.rejected:
        statusColor = AppColors.error;
        statusIcon = FontAwesomeIcons.circleXmark;
        statusText = 'مرفوضة';
        cardBorderColor = AppColors.error.withOpacity(0.3);
        break;
      case LeaveStatus.pending:
      default:
        statusColor = AppColors.warning;
        statusIcon = FontAwesomeIcons.clock;
        statusText = 'قيد المراجعة';
        cardBorderColor = AppColors.warning.withOpacity(0.3);
        break;
    }

    IconData leaveTypeIcon;
    switch (leaveRequest.leaveType) {
      case 'إجازة سنوية':
        leaveTypeIcon = FontAwesomeIcons.umbrellaBeach;
        break;
      case 'إجازة مرضية':
        leaveTypeIcon = FontAwesomeIcons.userDoctor;
        break;
      case 'إجازة طارئة':
        leaveTypeIcon = FontAwesomeIcons.triangleExclamation;
        break;
      case 'إجازة أمومة':
        leaveTypeIcon = FontAwesomeIcons.baby;
        break;
      case 'إجازة حج':
        leaveTypeIcon = FontAwesomeIcons.kaaba;
        break;
      default:
        leaveTypeIcon = FontAwesomeIcons.calendar;
    }

    return Card(
      elevation: 4,
      shadowColor: statusColor.withOpacity(0.2),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: cardBorderColor, width: 1),
        ),
        child: Column(
          children: [
            // Header with status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.05),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: FaIcon(
                      leaveTypeIcon,
                      color: statusColor,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          leaveRequest.leaveType,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'طلب رقم: ${leaveRequest.id}',
                          style: const TextStyle(
                            fontSize: 11,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FaIcon(
                          statusIcon,
                          color: statusColor,
                          size: 12,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          statusText,
                          style: TextStyle(
                            color: statusColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Date range and duration
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.calendarDays,
                          'تاريخ البداية',
                          DateFormat('dd/MM/yyyy').format(leaveRequest.startDate),
                          AppColors.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.calendarCheck,
                          'تاريخ النهاية',
                          DateFormat('dd/MM/yyyy').format(leaveRequest.endDate),
                          AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.clock,
                          'المدة',
                          '${leaveRequest.numberOfDays} يوم',
                          AppColors.accent,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildInfoItem(
                          FontAwesomeIcons.paperPlane,
                          'تاريخ الطلب',
                          DateFormat('dd/MM/yyyy').format(leaveRequest.requestDate),
                          AppColors.info,
                        ),
                      ),
                    ],
                  ),

                  // Reason
                  if (leaveRequest.reason.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.grey50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.border),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              FaIcon(
                                FontAwesomeIcons.comment,
                                color: AppColors.textSecondary,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'السبب:',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Text(
                            leaveRequest.reason,
                            style: const TextStyle(
                              fontSize: 13,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Approver comments
                  if (leaveRequest.approverComments != null && 
                      leaveRequest.approverComments!.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: statusColor.withOpacity(0.2)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              FaIcon(
                                FontAwesomeIcons.userCheck,
                                color: statusColor,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'تعليق المدير:',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: statusColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Text(
                            leaveRequest.approverComments!,
                            style: TextStyle(
                              fontSize: 13,
                              color: statusColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          FaIcon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
