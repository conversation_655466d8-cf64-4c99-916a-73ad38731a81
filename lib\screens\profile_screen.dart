import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../providers/auth_provider.dart';
import '../utils/app_colors.dart';
import '../widgets/profile_info_card.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('بيانات الموظف'),
        actions: [
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.arrowsRotate),
            onPressed: () async {
              final authProvider = Provider.of<AuthProvider>(context, listen: false);
              await authProvider.refreshEmployeeData();

              if (authProvider.errorMessage != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(authProvider.errorMessage!),
                    backgroundColor: Colors.red,
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديث البيانات بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
          ),
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.pen),
            onPressed: () {
              // TODO: Implement edit profile
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('ميزة تعديل البيانات ستكون متاحة قريباً'),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, _) {
          final user = authProvider.currentUser;
          
          if (user == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Profile header
                Card(
                  elevation: 8,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primaryLight.withOpacity(0.05),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(50),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withOpacity(0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: const FaIcon(
                            FontAwesomeIcons.userTie,
                            color: AppColors.white,
                            size: 50,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          user.name,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          user.position.isNotEmpty ? user.position : 'موظف',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user.departmentName.isNotEmpty ? user.departmentName : 'غير محدد',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (user.intId != null && user.intId!.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              'رقم الموظف: ${user.intId}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textSecondary,
                                fontStyle: FontStyle.italic,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Personal Information
                ProfileInfoCard(
                  title: 'المعلومات الشخصية',
                  icon: FontAwesomeIcons.user,
                  color: AppColors.primary,
                  items: [
                    ProfileInfoItem(
                      label: 'الاسم الكامل',
                      value: user.name,
                      icon: FontAwesomeIcons.signature,
                    ),
                    if (user.nationalNumber != null && user.nationalNumber!.isNotEmpty)
                      ProfileInfoItem(
                        label: 'الرقم الوطني',
                        value: user.nationalNumber!,
                        icon: FontAwesomeIcons.idCardClip,
                      ),
                    ProfileInfoItem(
                      label: 'البريد الإلكتروني',
                      value: user.email.isNotEmpty ? user.email : 'غير محدد',
                      icon: FontAwesomeIcons.envelope,
                    ),
                    ProfileInfoItem(
                      label: 'رقم الهاتف',
                      value: user.phoneNumber.isNotEmpty ? user.phoneNumber : 'غير محدد',
                      icon: FontAwesomeIcons.phone,
                    ),
                    if (user.connectedWithComp != null)
                      ProfileInfoItem(
                        label: 'حالة الاتصال بالشركة',
                        value: user.connectedWithComp == true ? 'متصل' : 'غير متصل',
                        icon: FontAwesomeIcons.wifi,
                      ),
                  ],
                ),
                const SizedBox(height: 16),

                // Work Information
                ProfileInfoCard(
                  title: 'المعلومات الوظيفية',
                  icon: FontAwesomeIcons.briefcase,
                  color: AppColors.secondary,
                  items: [
                    if (user.intId != null && user.intId!.isNotEmpty)
                      ProfileInfoItem(
                        label: 'رقم الموظف (النظام)',
                        value: user.intId!,
                        icon: FontAwesomeIcons.hashtag,
                      ),
                    ProfileInfoItem(
                      label: 'رقم الموظف',
                      value: user.employeeId.isNotEmpty ? user.employeeId : 'غير محدد',
                      icon: FontAwesomeIcons.idCard,
                    ),
                    ProfileInfoItem(
                      label: 'المنصب',
                      value: user.position.isNotEmpty ? user.position : 'غير محدد',
                      icon: FontAwesomeIcons.userTie,
                    ),
                    ProfileInfoItem(
                      label: 'القسم',
                      value: user.departmentName.isNotEmpty ? user.departmentName : 'غير محدد',
                      icon: FontAwesomeIcons.building,
                    ),
                    ProfileInfoItem(
                      label: 'المدير المباشر',
                      value: user.managerName.isNotEmpty ? user.managerName : 'غير محدد',
                      icon: FontAwesomeIcons.userCheck,
                    ),
                    if (user.coachName.isNotEmpty)
                      ProfileInfoItem(
                        label: 'المدرب/الموجه',
                        value: user.coachName,
                        icon: FontAwesomeIcons.chalkboardTeacher,
                      ),
                    ProfileInfoItem(
                      label: 'تاريخ الالتحاق',
                      value: DateFormat('dd/MM/yyyy').format(user.joinDate),
                      icon: FontAwesomeIcons.calendarPlus,
                    ),
                    ProfileInfoItem(
                      label: 'سنوات الخدمة',
                      value: '${DateTime.now().year - user.joinDate.year} سنة',
                      icon: FontAwesomeIcons.award,
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Financial Information
                ProfileInfoCard(
                  title: 'المعلومات المالية',
                  icon: FontAwesomeIcons.moneyBill,
                  color: AppColors.accent,
                  items: [
                    ProfileInfoItem(
                      label: 'الراتب الأساسي',
                      value: '${NumberFormat('#,###').format(user.salary)} ريال',
                      icon: FontAwesomeIcons.coins,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }
}
