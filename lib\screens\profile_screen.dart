import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../providers/auth_provider.dart';
import '../utils/app_colors.dart';
import '../widgets/profile_info_card.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('بيانات الموظف'),
        actions: [
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.pen),
            onPressed: () {
              // TODO: Implement edit profile
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('ميزة تعديل البيانات ستكون متاحة قريباً'),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, _) {
          final user = authProvider.currentUser;
          
          if (user == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Profile header
                Card(
                  elevation: 8,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primaryLight.withOpacity(0.05),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(50),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withOpacity(0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: const FaIcon(
                            FontAwesomeIcons.userTie,
                            color: AppColors.white,
                            size: 50,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          user.name,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          user.position,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user.department,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Personal Information
                ProfileInfoCard(
                  title: 'المعلومات الشخصية',
                  icon: FontAwesomeIcons.user,
                  color: AppColors.primary,
                  items: [
                    ProfileInfoItem(
                      label: 'الاسم الكامل',
                      value: user.name,
                      icon: FontAwesomeIcons.signature,
                    ),
                    ProfileInfoItem(
                      label: 'البريد الإلكتروني',
                      value: user.email,
                      icon: FontAwesomeIcons.envelope,
                    ),
                    ProfileInfoItem(
                      label: 'رقم الهاتف',
                      value: user.phoneNumber,
                      icon: FontAwesomeIcons.phone,
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Work Information
                ProfileInfoCard(
                  title: 'المعلومات الوظيفية',
                  icon: FontAwesomeIcons.briefcase,
                  color: AppColors.secondary,
                  items: [
                    ProfileInfoItem(
                      label: 'رقم الموظف',
                      value: user.employeeId,
                      icon: FontAwesomeIcons.idCard,
                    ),
                    ProfileInfoItem(
                      label: 'المنصب',
                      value: user.position,
                      icon: FontAwesomeIcons.userTie,
                    ),
                    ProfileInfoItem(
                      label: 'القسم',
                      value: user.department,
                      icon: FontAwesomeIcons.building,
                    ),
                    ProfileInfoItem(
                      label: 'المدير المباشر',
                      value: user.manager,
                      icon: FontAwesomeIcons.userCheck,
                    ),
                    ProfileInfoItem(
                      label: 'تاريخ الالتحاق',
                      value: DateFormat('dd/MM/yyyy').format(user.joinDate),
                      icon: FontAwesomeIcons.calendarPlus,
                    ),
                    ProfileInfoItem(
                      label: 'سنوات الخدمة',
                      value: '${DateTime.now().year - user.joinDate.year} سنة',
                      icon: FontAwesomeIcons.award,
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Financial Information
                ProfileInfoCard(
                  title: 'المعلومات المالية',
                  icon: FontAwesomeIcons.moneyBill,
                  color: AppColors.accent,
                  items: [
                    ProfileInfoItem(
                      label: 'الراتب الأساسي',
                      value: '${NumberFormat('#,###').format(user.salary)} ريال',
                      icon: FontAwesomeIcons.coins,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }
}
