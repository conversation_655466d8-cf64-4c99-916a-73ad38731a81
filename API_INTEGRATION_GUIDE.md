# دليل تكامل API الجديد

## نظرة عامة

تم تحديث التطبيق لدعم API الجديد `/api/employee/me` الذي يجلب بيانات الموظف المسجل حالياً.

## التحديثات المنجزة

### 1. تحديث نموذج الموظف (Employee Model)

تم إضافة الحقول الجديدة التالية:
- `intId`: معرف الموظف الداخلي
- `departmentId`: معرف واسم القسم [id, name]
- `parentId`: معرف واسم المدير [id, name]
- `coachId`: معرف واسم المدرب [id, name]
- `connectedWithComp`: حالة الاتصال بالشركة
- `nationalNumber`: الرقم الوطني

### 2. إضافة endpoint جديد

```dart
// في lib/config/api_config.dart
static const String employeeEndpoint = '/api/employee/me';
```

### 3. تطوير خدمة API

تم إضافة دالة `getEmployeeData()` في `ApiService` لجلب بيانات الموظف:

```dart
static Future<Map<String, dynamic>> getEmployeeData({
  required String sessionId,
}) async {
  // Implementation...
}
```

### 4. تحديث خدمة المصادقة

تم تحديث `AuthService` لاستخدام API الجديد:
- جلب بيانات الموظف من API بعد تسجيل الدخول
- استخدام البيانات التجريبية كـ fallback
- معالجة انتهاء صلاحية الجلسة

### 5. تحسين مزود المصادقة

تم تحديث `AuthProvider` لـ:
- معالجة أفضل للأخطاء
- إضافة دالة `refreshEmployeeData()`
- عرض رسائل خطأ مفصلة

## كيفية الاستخدام

### 1. تكوين الخادم

```dart
// في lib/config/api_config.dart
static const String baseUrl = 'http://your-server.com:8069';
```

### 2. تسجيل الدخول

عند تسجيل الدخول بنجاح، سيقوم التطبيق تلقائياً بـ:
1. حفظ `session_id` و `user_id`
2. استدعاء `/api/employee/me` لجلب بيانات الموظف
3. عرض البيانات في واجهة المستخدم

### 3. معالجة الأخطاء

التطبيق يتعامل مع الحالات التالية:
- انتهاء صلاحية الجلسة (401)
- عدم وجود بيانات موظف
- أخطاء الشبكة
- استخدام البيانات التجريبية كـ fallback

## تنسيق API المطلوب

### الطلب
```http
GET /api/employee/me
Cookie: session_id=your_session_id
```

### الاستجابة المتوقعة
```json
{
  "int_id": "123",
  "name": "أحمد محمد",
  "department_id": [1, "قسم تقنية المعلومات"],
  "parent_id": [2, "سارة أحمد"],
  "coach_id": [3, "محمد علي"],
  "connected_with_comp": true,
  "national_number": "1234567890"
}
```

### في حالة الخطأ
```json
{
  "error": "الموظف غير مرتبط بالمستخدم الحالي"
}
```

## الاختبار

تم إنشاء اختبارات للتأكد من:
- صحة تحويل البيانات من API
- معالجة الحقول المفقودة
- التعامل مع القيم الفارغة

```bash
flutter test test/api_integration_test.dart
```

## الميزات الجديدة

1. **جلب بيانات حقيقية**: بدلاً من البيانات التجريبية
2. **معلومات إضافية**: رقم وطني، معلومات المدرب، حالة الاتصال
3. **معالجة أفضل للأخطاء**: رسائل واضحة للمستخدم
4. **fallback آمن**: استخدام البيانات التجريبية عند فشل API

## المتطلبات

- خادم يدعم endpoint `/api/employee/me`
- session_id صالح من عملية تسجيل الدخول
- استجابة بالتنسيق المحدد أعلاه
