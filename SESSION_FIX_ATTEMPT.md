# محاولة إصلاح مشكلة الجلسة

## المشكلة المحددة

من الـ logs السابقة:
✅ **تسجيل الدخول نجح** - حصلنا على session_id صحيح
✅ **POST request نجح** - status 200
❌ **Odoo Session Expired** - الخادم لا يتعرف على الـ session

## التحليل

المشكلة أن Odoo لا يتعرف على الـ session_id بالطريقة التي نرسلها. قد يكون السبب:

1. **طريقة إرسال Cookie خاطئة**
2. **تنسيق الطلب غير متوافق مع Odoo**
3. **الـ session_id ينتهي بسرعة**

## الحل الجديد المطبق

### ✅ طريقة مبسطة ومرنة

```dart
// جرب الطريقة الأبسط أولاً
var response = await http.post(
  url,
  headers: {
    'Content-Type': 'application/json',
    'Cookie': 'session_id=$sessionId',
  },
  body: jsonEncode({}), // body فارغ
);

// إذا فشلت، جرب مع jsonrpc format
if (response.statusCode != 200 || response.body.contains('Session Expired')) {
  response = await http.post(
    url,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': 'session_id=$sessionId',
    },
    body: jsonEncode({
      'jsonrpc': '2.0',
      'method': 'call',
      'params': {},
      'id': null,
    }),
  );
}
```

### ✅ معالجة أفضل لـ Session Expired

```dart
if (errorMessage.contains('Session Expired') || 
    errorMessage.contains('SessionExpiredException') ||
    error['code'] == 100) {
  errorMessage = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
  requiresLogin = true;
}
```

## اختبار الإصلاح

### 1. أعد تشغيل التطبيق
```bash
flutter hot restart
```

### 2. جرب تسجيل الدخول
- Database: "BSIC-Data"
- Username: "a"
- Password: "a"

### 3. راقب الـ Logs الجديدة

#### ✅ إذا نجحت الطريقة الأولى:
```
I/flutter: First attempt - Response status: 200
I/flutter: First attempt - Response body: {int_id: 474, name: ..., ...}
I/flutter: Found employee data directly in response
```

#### ⚠️ إذا فشلت الأولى ونجحت الثانية:
```
I/flutter: First attempt - Response status: 200
I/flutter: First attempt - Response body: {...Session Expired...}
I/flutter: First attempt failed, trying with jsonrpc format...
I/flutter: Employee data response status: 200
I/flutter: Employee data response body: {int_id: 474, ...}
```

#### ❌ إذا فشلت كلا الطريقتين:
```
I/flutter: API Error: انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.
I/flutter: Requires login: true
```

## النتائج المتوقعة

### ✅ أفضل سيناريو:
- جلب بيانات الموظف بنجاح
- عرض البيانات الحقيقية في الواجهة

### ⚠️ سيناريو متوسط:
- رسالة: "انتهت صلاحية الجلسة"
- التطبيق يطلب تسجيل دخول جديد

### 📋 سيناريو احتياطي:
- رسالة: "تم تسجيل الدخول بنجاح ولكن لم يتم العثور على بيانات الموظف"
- التطبيق يعمل بالبيانات التجريبية

## إذا استمرت المشكلة

قد نحتاج لـ:
1. **فحص إعدادات Odoo** للـ session management
2. **تجربة endpoint مختلف** للمصادقة
3. **استخدام طريقة مختلفة** لإرسال الـ credentials

## الملفات المحدثة

- `lib/services/api_service.dart` - طريقة مبسطة ومرنة لإرسال الطلبات

جرب الآن وأخبرني بالنتيجة! 🚀
