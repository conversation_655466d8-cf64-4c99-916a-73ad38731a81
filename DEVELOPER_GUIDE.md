# دليل المطور - نظام تسجيل الدخول

## نظرة عامة

تم تطوير نظام تسجيل الدخول ليدعم طريقتين:
1. **API الحقيقي**: للاتصال بخادم Odoo أو أي خادم يدعم نفس البروتوكول
2. **البيانات التجريبية**: للاختبار والتطوير السريع

## الملفات المحدثة

### 1. `lib/services/api_service.dart`
خدمة جديدة للتعامل مع API الحقيقي:
- إرسال طلبات HTTP إلى الخادم
- معالجة الاستجابات وإدارة الأخطاء
- دعم timeout والتحقق من الاتصال

### 2. `lib/services/auth_service.dart`
تم تحديثها لتدعم:
- تسجيل الدخول عبر API أو البيانات التجريبية
- حفظ معلومات الجلسة (session_id, database)
- إدارة أفضل للأخطاء

### 3. `lib/providers/auth_provider.dart`
تم إضافة:
- دعم رسائل الأخطاء المفصلة
- معالجة أفضل لحالات الفشل

### 4. `lib/screens/login_screen.dart`
واجهة محدثة تتضمن:
- مفتاح للتبديل بين API والوضع التجريبي
- حقل اسم قاعدة البيانات
- رسائل خطأ محسنة

### 5. `lib/config/api_config.dart`
ملف تكوين جديد لإدارة:
- عناوين الخادم
- إعدادات الاتصال
- تفعيل/إلغاء وضع التطوير

## كيفية الاستخدام

### للمطورين
1. **تغيير عنوان الخادم**:
   ```dart
   // في lib/config/api_config.dart
   static const String baseUrl = 'http://your-server.com:8069';
   ```

2. **تفعيل/إلغاء وضع التطوير**:
   ```dart
   static const bool enableDebugMode = false; // للإنتاج
   ```

### للمستخدمين
1. **استخدام API الحقيقي**:
   - فعّل مفتاح "استخدام API الحقيقي"
   - أدخل اسم قاعدة البيانات
   - أدخل بيانات المستخدم

2. **استخدام البيانات التجريبية**:
   - اتركه في الوضع التجريبي
   - اختر من القائمة المنسدلة أو اكتب يدوياً
   - استخدم كلمات المرور المحددة

## تنسيق API المطلوب

### طلب تسجيل الدخول
```json
POST /api/login
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "call",
  "params": {
    "db": "database_name",
    "login": "username", 
    "password": "password"
  },
  "id": null
}
```

### الاستجابة المتوقعة - نجاح
```json
{
  "jsonrpc": "2.0",
  "result": {
    "success": true,
    "session_id": "unique_session_id",
    "user_id": 123,
    "message": "تم تسجيل الدخول بنجاح"
  },
  "id": null
}
```

### الاستجابة المتوقعة - فشل
```json
{
  "jsonrpc": "2.0",
  "result": {
    "success": false,
    "message": "بيانات الدخول غير صحيحة"
  },
  "id": null
}
```

### الاستجابة في حالة خطأ
```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": 400,
    "message": "رسالة الخطأ"
  },
  "id": null
}
```

## اختبار النظام

### اختبار API
1. تأكد من تشغيل الخادم
2. تحقق من صحة عنوان الخادم في `api_config.dart`
3. جرب تسجيل الدخول مع بيانات صحيحة

### اختبار البيانات التجريبية
استخدم أي من هذه البيانات:
- `admin` / `admin123`
- `ahmed.ali` / `password123`
- `sara.mohamed` / `password123`
- `omar.hassan` / `password123`

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الاتصال**: تحقق من عنوان الخادم
2. **انتهاء المهلة**: تحقق من سرعة الإنترنت
3. **بيانات خاطئة**: تأكد من صحة اسم قاعدة البيانات

### تفعيل وضع التطوير
```dart
// في lib/config/api_config.dart
static const bool enableDebugMode = true;
```
سيظهر تفاصيل الطلبات والاستجابات في وحدة التحكم.
