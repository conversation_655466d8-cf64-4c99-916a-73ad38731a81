# الحل النهائي لمشكلة Session Expired

## 🔍 تحليل المشكلة

المشكلة **ليست في Flutter** بل في **API الخاص بك**:

1. ✅ تسجيل الدخول ينجح ويعطي `session_id` صحيح
2. ✅ الطلبات تصل للخادم بنجاح (status 200)
3. ❌ Odoo يقول "Session Expired" فوراً بعد تسجيل الدخول

**السبب**: `auth='user'` في API يتطلب session مرتبط بـ database، لكن الـ session من `/api/login` لا يتم ربطه بشكل صحيح.

## 🛠️ الحل - تعديل API

### ✅ الحل الأول (الأفضل):

```python
class EmployeeAPI(http.Controller):

    @http.route('/api/employee/me', type='json', auth='public', csrf=False, methods=['POST'])
    def get_employee(self):
        # الحصول على معلومات من الطلب
        params = request.jsonrequest.get('params', {})
        session_id = params.get('session_id')
        database = params.get('database')
        user_id = params.get('user_id')
        
        if not all([session_id, database, user_id]):
            return {'error': 'معلومات الجلسة غير مكتملة'}
        
        try:
            # الاتصال بقاعدة البيانات مباشرة
            registry = odoo.registry(database)
            with registry.cursor() as cr:
                env = odoo.api.Environment(cr, int(user_id), {})
                user = env['res.users'].browse(int(user_id))
                
                if not user.exists():
                    return {'error': 'المستخدم غير موجود'}
                
                employee = user.employee_id
                if not employee:
                    return {'error': 'الموظف غير مرتبط بالمستخدم الحالي'}

                return {
                    'int_id': employee.int_id,
                    'name': employee.name,
                    'department_id': employee.department_id and [employee.department_id.id, employee.department_id.name],
                    'parent_id': employee.parent_id and [employee.parent_id.id, employee.parent_id.name],
                    'coach_id': employee.coach_id and [employee.coach_id.id, employee.coach_id.name],
                    'connected_with_comp': employee.connected_with_comp,
                    'national_number': employee.national_number,
                }
                
        except Exception as e:
            return {'error': f'خطأ في الخادم: {str(e)}'}
```

### ✅ الحل الثاني (إذا كان الأول معقد):

```python
class EmployeeAPI(http.Controller):

    @http.route('/api/employee/me', type='json', auth='none', csrf=False, methods=['POST'])
    def get_employee(self):
        # الحصول على session_id من Cookie
        session_id = request.httprequest.cookies.get('session_id')
        
        if not session_id:
            return {'error': 'لا توجد جلسة صالحة'}
        
        try:
            # محاولة الحصول على المستخدم من الجلسة
            # هذا يحتاج تعديل حسب إعداد Odoo الخاص بك
            session_store = request.session_store
            session_data = session_store.get(session_id)
            
            if not session_data or 'uid' not in session_data:
                return {'error': 'جلسة غير صالحة أو منتهية الصلاحية'}
            
            user_id = session_data['uid']
            db_name = session_data.get('db')
            
            # باقي الكود...
            
        except Exception as e:
            return {'error': f'خطأ في الخادم: {str(e)}'}
```

## 📱 تحديث Flutter (تم تطبيقه)

تم تحديث Flutter ليرسل المعلومات المطلوبة:

```dart
// في ApiService.getEmployeeData()
body: jsonEncode({
  'jsonrpc': '2.0',
  'method': 'call',
  'params': {
    'session_id': sessionId,
    'database': database,
    'user_id': userId,
  },
  'id': null,
}),
```

## 🧪 اختبار الحل

### 1. طبق تعديل API (الحل الأول)
### 2. أعد تشغيل Odoo server
### 3. أعد تشغيل Flutter app:
```bash
flutter hot restart
```

### 4. جرب تسجيل الدخول

## 📋 النتائج المتوقعة

### ✅ إذا نجح الحل:
```
I/flutter: Employee data response status: 200
I/flutter: Employee data response body: {int_id: 474, name: اسم الموظف, ...}
I/flutter: Found employee data directly in response
```

### ❌ إذا لم ينجح:
```
I/flutter: Employee data response body: {error: الموظف غير مرتبط بالمستخدم الحالي}
```

## 🔧 استكشاف الأخطاء

### إذا ظهر "المستخدم غير موجود":
- تحقق من أن `user_id` صحيح في قاعدة البيانات

### إذا ظهر "الموظف غير مرتبط":
- تحقق من أن المستخدم له `employee_id` في Odoo
- في Odoo: Settings > Users & Companies > Users > [اختر المستخدم] > تأكد من ربطه بموظف

### إذا ظهر "قاعدة البيانات غير محددة":
- تحقق من أن اسم قاعدة البيانات صحيح

## 🎯 الخلاصة

**المشكلة كانت في API وليس في Flutter**. الحل يتطلب تعديل API ليتعامل مع الجلسات بطريقة صحيحة.

بعد تطبيق الحل، ستحصل على بيانات الموظف الحقيقية في التطبيق! 🚀
