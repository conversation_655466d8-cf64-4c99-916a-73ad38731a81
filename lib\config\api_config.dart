class ApiConfig {
  // إعدادات الخادم - يجب تغييرها حسب بيئة العمل
  static const String baseUrl = 'http://192.168.204.130:8069'; // أو رابط الخادم الخاص بك
  static const String loginEndpoint = '/api/login';
  
  // إعدادات الاتصال
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // رؤوس HTTP الافتراضية
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  // إعدادات التطبيق
  static const bool enableDebugMode = true; // تفعيل وضع التطوير
  static const bool enableMockData = true; // تفعيل البيانات التجريبية
  
  // الحصول على رابط كامل للـ endpoint
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  // التحقق من صحة إعدادات الخادم
  static bool isValidServerUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }
}
