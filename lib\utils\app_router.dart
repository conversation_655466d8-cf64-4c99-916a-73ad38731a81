import 'package:go_router/go_router.dart';
import '../screens/login_screen.dart';
import '../screens/main_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/leave_balance_screen.dart';
import '../screens/leave_request_screen.dart';

class AppRouter {
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: '/login',
      routes: [
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        ShellRoute(
          builder: (context, state, child) => MainScreen(child: child),
          routes: [
            GoRoute(
              path: '/dashboard',
              name: 'dashboard',
              builder: (context, state) => const DashboardScreen(),
            ),
            GoRoute(
              path: '/profile',
              name: 'profile',
              builder: (context, state) => const ProfileScreen(),
            ),
            GoRoute(
              path: '/leave-balance',
              name: 'leave-balance',
              builder: (context, state) => const LeaveBalanceScreen(),
            ),
            GoRoute(
              path: '/leave-request',
              name: 'leave-request',
              builder: (context, state) => const LeaveRequestScreen(),
            ),
          ],
        ),
      ],
    );
  }
}
