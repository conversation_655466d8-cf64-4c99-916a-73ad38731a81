import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/auth_provider.dart';
import '../providers/employee_provider.dart';
import '../utils/app_colors.dart';
import '../widgets/leave_balance_card.dart';
import '../widgets/leave_history_card.dart';

class LeaveBalanceScreen extends StatefulWidget {
  const LeaveBalanceScreen({super.key});

  @override
  State<LeaveBalanceScreen> createState() => _LeaveBalanceScreenState();
}

class _LeaveBalanceScreenState extends State<LeaveBalanceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final employeeProvider = Provider.of<EmployeeProvider>(
      context,
      listen: false,
    );

    if (authProvider.currentUser != null) {
      employeeProvider.loadEmployeeData(authProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('رصيد الإجازات'),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(
              icon: FaIcon(FontAwesomeIcons.chartPie, size: 18),
              text: 'الأرصدة',
            ),
            Tab(
              icon: FaIcon(FontAwesomeIcons.clockRotateLeft, size: 18),
              text: 'التاريخ',
            ),
          ],
        ),
      ),
      body: Consumer2<AuthProvider, EmployeeProvider>(
        builder: (context, authProvider, employeeProvider, _) {
          if (employeeProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return RefreshIndicator(
            onRefresh: () async {
              _loadData();
            },
            child: TabBarView(
              controller: _tabController,
              children: [
                // Leave Balances Tab
                _buildBalancesTab(employeeProvider),

                // Leave History Tab
                _buildHistoryTab(employeeProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBalancesTab(EmployeeProvider employeeProvider) {
    final leaveBalances = employeeProvider.leaveBalances;

    if (leaveBalances.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.calendarXmark,
              color: AppColors.grey400,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات أرصدة إجازات',
              style: TextStyle(color: AppColors.textSecondary, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Summary card
          Card(
            elevation: 8,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primaryLight],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                children: [
                  const FaIcon(
                    FontAwesomeIcons.calendarCheck,
                    color: AppColors.white,
                    size: 40,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'إجمالي الإجازات المتبقية',
                    style: TextStyle(
                      color: AppColors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${employeeProvider.getTotalRemainingDays()} يوم',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'من أصل ${leaveBalances.fold(0, (sum, balance) => sum + balance.totalDays)} يوم',
                    style: TextStyle(
                      color: AppColors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Individual leave balances
          ...leaveBalances.map(
            (balance) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: LeaveBalanceCard(leaveBalance: balance),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab(EmployeeProvider employeeProvider) {
    final leaveRequests = employeeProvider.leaveRequests;

    if (leaveRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.folderOpen,
              color: AppColors.grey400,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد تاريخ إجازات',
              style: TextStyle(color: AppColors.textSecondary, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Statistics
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الطلبات',
                  '${leaveRequests.length}',
                  FontAwesomeIcons.fileLines,
                  AppColors.info,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'الطلبات المعتمدة',
                  '${leaveRequests.where((r) => r.status.toString().contains('approved')).length}',
                  FontAwesomeIcons.circleCheck,
                  AppColors.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Leave history
          ...leaveRequests.map(
            (request) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: LeaveHistoryCard(leaveRequest: request),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            FaIcon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
