class LeaveBalance {
  final String leaveType;
  final int totalDays;
  final int usedDays;
  final int remainingDays;
  final String description;

  LeaveBalance({
    required this.leaveType,
    required this.totalDays,
    required this.usedDays,
    required this.remainingDays,
    required this.description,
  });

  factory LeaveBalance.fromJson(Map<String, dynamic> json) {
    return LeaveBalance(
      leaveType: json['leaveType'],
      totalDays: json['totalDays'],
      usedDays: json['usedDays'],
      remainingDays: json['remainingDays'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'leaveType': leaveType,
      'totalDays': totalDays,
      'usedDays': usedDays,
      'remainingDays': remainingDays,
      'description': description,
    };
  }
}
