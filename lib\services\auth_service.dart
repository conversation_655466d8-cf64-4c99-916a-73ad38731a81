import 'package:shared_preferences/shared_preferences.dart';
import '../models/employee.dart';
import 'mock_data_service.dart';
import 'api_service.dart';

class AuthService {
  static const String _isLoggedInKey = 'isLoggedIn';
  static const String _currentUserIdKey = 'currentUserId';
  static const String _sessionIdKey = 'sessionId';
  static const String _databaseKey = 'database';

  // Mock credentials for demo (fallback)
  static const Map<String, String> _mockCredentials = {
    'admin': 'admin123',
    'ahmed.ali': 'password123',
    'sara.mohamed': 'password123',
    'omar.hassan': 'password123',
  };

  /// تسجيل الدخول باستخدام API أو البيانات التجريبية
  Future<Map<String, dynamic>> login(String username, String password, {String? database}) async {
    try {
      // إذا تم تحديد قاعدة البيانات، استخدم API الحقيقي
      if (database != null && database.isNotEmpty) {
        final result = await ApiService.login(
          database: database,
          username: username,
          password: password,
        );

        print('AuthService: API login result: $result');

        if (result['success'] == true) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_isLoggedInKey, true);
          await prefs.setString(_currentUserIdKey, result['user_id'].toString());
          await prefs.setString(_sessionIdKey, result['session_id']);
          await prefs.setString(_databaseKey, database);

          return {
            'success': true,
            'message': result['message'],
            'user_id': result['user_id'],
            'session_id': result['session_id'],
          };
        } else {
          return {
            'success': false,
            'message': result['message'],
          };
        }
      } else {
        // استخدام البيانات التجريبية كـ fallback
        if (_mockCredentials.containsKey(username) &&
            _mockCredentials[username] == password) {

          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_isLoggedInKey, true);
          await prefs.setString(_currentUserIdKey, username);

          return {
            'success': true,
            'message': 'تم تسجيل الدخول بنجاح (وضع تجريبي)',
            'user_id': username,
          };
        } else {
          return {
            'success': false,
            'message': 'اسم المستخدم أو كلمة المرور غير صحيحة',
          };
        }
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الدخول: ${e.toString()}',
      };
    }
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_isLoggedInKey);
    await prefs.remove(_currentUserIdKey);
    await prefs.remove(_sessionIdKey);
    await prefs.remove(_databaseKey);
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  Future<String?> getCurrentUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_currentUserIdKey);
  }

  Future<String?> getSessionId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_sessionIdKey);
  }

  Future<String?> getDatabase() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_databaseKey);
  }

  Future<Employee?> getCurrentUser() async {
    final sessionId = await getSessionId();
    final database = await getDatabase();

    // إذا كان لدينا session_id و database، استخدم API الحقيقي
    if (sessionId != null && database != null && database.isNotEmpty) {
      try {
        final result = await fetchEmployeeData();

        if (result['success'] == true && result['data'] != null) {
          return Employee.fromApiResponse(result['data']);
        } else {
          // في حالة فشل API، قد نحتاج لإعادة تسجيل الدخول
          if (result['requires_login'] == true) {
            await logout(); // مسح بيانات الجلسة المنتهية الصلاحية
          }
          return null;
        }
      } catch (e) {
        // في حالة حدوث خطأ، استخدم البيانات التجريبية كـ fallback
        final userId = await getCurrentUserId();
        if (userId != null) {
          return MockDataService.getEmployeeById(userId);
        }
        return null;
      }
    } else {
      // استخدام البيانات التجريبية
      final userId = await getCurrentUserId();
      if (userId != null) {
        return MockDataService.getEmployeeById(userId);
      }
      return null;
    }
  }

  /// جلب بيانات الموظف من API
  Future<Map<String, dynamic>> fetchEmployeeData() async {
    final sessionId = await getSessionId();

    if (sessionId != null) {
      return await ApiService.getEmployeeData(sessionId: sessionId);
    } else {
      return {
        'success': false,
        'message': 'لا توجد جلسة نشطة',
      };
    }
  }

  List<String> getAvailableUsernames() {
    return _mockCredentials.keys.toList();
  }
}
