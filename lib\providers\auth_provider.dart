import 'package:flutter/material.dart';
import '../models/employee.dart';
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();

  bool _isLoggedIn = false;
  Employee? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  bool get isLoggedIn => _isLoggedIn;
  Employee? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();

    _isLoggedIn = await _authService.isLoggedIn();
    if (_isLoggedIn) {
      _currentUser = await _authService.getCurrentUser();
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> login(String username, String password, {String? database}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    final result = await _authService.login(username, password, database: database);

    if (result['success'] == true) {
      _isLoggedIn = true;
      _currentUser = await _authService.getCurrentUser();
      _errorMessage = null;
    } else {
      _isLoggedIn = false;
      _currentUser = null;
      _errorMessage = result['message'];
    }

    _isLoading = false;
    notifyListeners();
    return result['success'] == true;
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    await _authService.logout();
    _isLoggedIn = false;
    _currentUser = null;

    _isLoading = false;
    notifyListeners();
  }

  List<String> getAvailableUsernames() {
    return _authService.getAvailableUsernames();
  }
}
