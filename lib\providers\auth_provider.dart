import 'package:flutter/material.dart';
import '../models/employee.dart';
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();

  bool _isLoggedIn = false;
  Employee? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  bool get isLoggedIn => _isLoggedIn;
  Employee? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();

    _isLoggedIn = await _authService.isLoggedIn();
    if (_isLoggedIn) {
      _currentUser = await _authService.getCurrentUser();
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> login(String username, String password, {String? database}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await _authService.login(username, password, database: database);

      print('AuthProvider: Login result: $result');
      print('AuthProvider: Success check: ${result['success']} == true = ${result['success'] == true}');

      if (result['success'] == true) {
        _isLoggedIn = true;

        // جلب بيانات الموظف بعد تسجيل الدخول بنجاح
        print('AuthProvider: Fetching current user...');
        _currentUser = await _authService.getCurrentUser();
        print('AuthProvider: Current user result: $_currentUser');

        if (_currentUser == null && database != null && database.isNotEmpty) {
          // إذا فشل في جلب بيانات الموظف من API، أظهر رسالة تحذيرية
          _errorMessage = 'تم تسجيل الدخول بنجاح ولكن لم يتم العثور على بيانات الموظف';
          print('AuthProvider: Failed to get employee data from API');
        } else {
          _errorMessage = null;
          print('AuthProvider: Successfully got employee data');
        }
      } else {
        _isLoggedIn = false;
        _currentUser = null;
        _errorMessage = result['message'] ?? 'فشل في تسجيل الدخول';
      }
    } catch (e) {
      _isLoggedIn = false;
      _currentUser = null;
      _errorMessage = 'حدث خطأ أثناء تسجيل الدخول: ${e.toString()}';
    }

    _isLoading = false;
    notifyListeners();
    return _isLoggedIn;
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    await _authService.logout();
    _isLoggedIn = false;
    _currentUser = null;

    _isLoading = false;
    notifyListeners();
  }

  /// إعادة جلب بيانات الموظف الحالي
  Future<bool> refreshEmployeeData() async {
    if (!_isLoggedIn) return false;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await _authService.fetchEmployeeData();

      if (result['success'] == true) {
        _currentUser = await _authService.getCurrentUser();
        _errorMessage = null;
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _errorMessage = result['message'];

        // إذا كانت المشكلة في انتهاء صلاحية الجلسة
        if (result['requires_login'] == true) {
          _isLoggedIn = false;
          _currentUser = null;
        }

        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'خطأ في جلب بيانات الموظف: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  List<String> getAvailableUsernames() {
    return _authService.getAvailableUsernames();
  }
}
