enum LeaveStatus { pending, approved, rejected }

class LeaveRequest {
  final String id;
  final String employeeId;
  final String leaveType;
  final DateTime startDate;
  final DateTime endDate;
  final int numberOfDays;
  final String reason;
  final LeaveStatus status;
  final DateTime requestDate;
  final String? approverComments;

  LeaveRequest({
    required this.id,
    required this.employeeId,
    required this.leaveType,
    required this.startDate,
    required this.endDate,
    required this.numberOfDays,
    required this.reason,
    required this.status,
    required this.requestDate,
    this.approverComments,
  });

  factory LeaveRequest.fromJson(Map<String, dynamic> json) {
    return LeaveRequest(
      id: json['id'],
      employeeId: json['employeeId'],
      leaveType: json['leaveType'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      numberOfDays: json['numberOfDays'],
      reason: json['reason'],
      status: LeaveStatus.values.firstWhere(
        (e) => e.toString() == 'LeaveStatus.${json['status']}',
      ),
      requestDate: DateTime.parse(json['requestDate']),
      approverComments: json['approverComments'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'leaveType': leaveType,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'numberOfDays': numberOfDays,
      'reason': reason,
      'status': status.toString().split('.').last,
      'requestDate': requestDate.toIso8601String(),
      'approverComments': approverComments,
    };
  }
}
