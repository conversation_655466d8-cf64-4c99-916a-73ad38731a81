import '../models/employee.dart';
import '../models/leave_balance.dart';
import '../models/leave_request.dart';

class MockDataService {
  static final List<Employee> _employees = [
    Employee(
      id: 'admin',
      name: 'أحمد محمد الإداري',
      email: '<EMAIL>',
      department: 'الإدارة العامة',
      position: 'مدير عام',
      phoneNumber: '+966501234567',
      joinDate: DateTime(2020, 1, 15),
      employeeId: 'EMP001',
      manager: 'الرئيس التنفيذي',
      salary: 15000.0,
      profileImage: '',
    ),
    Employee(
      id: 'ahmed.ali',
      name: 'أحمد علي السعيد',
      email: '<EMAIL>',
      department: 'تقنية المعلومات',
      position: 'مطور برمجيات',
      phoneNumber: '+966501234568',
      joinDate: DateTime(2021, 3, 10),
      employeeId: 'EMP002',
      manager: 'سارة محمد',
      salary: 8000.0,
      profileImage: '',
    ),
    Employee(
      id: 'sara.mohamed',
      name: 'سارة محمد الأحمد',
      email: '<EMAIL>',
      department: 'تقنية المعلومات',
      position: 'مديرة تقنية المعلومات',
      phoneNumber: '+966501234569',
      joinDate: DateTime(2019, 6, 1),
      employeeId: 'EMP003',
      manager: 'أحمد محمد الإداري',
      salary: 12000.0,
      profileImage: '',
    ),
    Employee(
      id: 'omar.hassan',
      name: 'عمر حسن الخالد',
      email: '<EMAIL>',
      department: 'الموارد البشرية',
      position: 'أخصائي موارد بشرية',
      phoneNumber: '+966501234570',
      joinDate: DateTime(2022, 1, 20),
      employeeId: 'EMP004',
      manager: 'أحمد محمد الإداري',
      salary: 7000.0,
      profileImage: '',
    ),
  ];

  static final Map<String, List<LeaveBalance>> _leaveBalances = {
    'admin': [
      LeaveBalance(
        leaveType: 'إجازة سنوية',
        totalDays: 30,
        usedDays: 10,
        remainingDays: 20,
        description: 'الإجازة السنوية المستحقة',
      ),
      LeaveBalance(
        leaveType: 'إجازة مرضية',
        totalDays: 15,
        usedDays: 3,
        remainingDays: 12,
        description: 'إجازة مرضية بشهادة طبية',
      ),
      LeaveBalance(
        leaveType: 'إجازة طارئة',
        totalDays: 5,
        usedDays: 1,
        remainingDays: 4,
        description: 'إجازة للظروف الطارئة',
      ),
    ],
    'ahmed.ali': [
      LeaveBalance(
        leaveType: 'إجازة سنوية',
        totalDays: 21,
        usedDays: 7,
        remainingDays: 14,
        description: 'الإجازة السنوية المستحقة',
      ),
      LeaveBalance(
        leaveType: 'إجازة مرضية',
        totalDays: 15,
        usedDays: 2,
        remainingDays: 13,
        description: 'إجازة مرضية بشهادة طبية',
      ),
      LeaveBalance(
        leaveType: 'إجازة طارئة',
        totalDays: 3,
        usedDays: 0,
        remainingDays: 3,
        description: 'إجازة للظروف الطارئة',
      ),
    ],
    'sara.mohamed': [
      LeaveBalance(
        leaveType: 'إجازة سنوية',
        totalDays: 25,
        usedDays: 12,
        remainingDays: 13,
        description: 'الإجازة السنوية المستحقة',
      ),
      LeaveBalance(
        leaveType: 'إجازة مرضية',
        totalDays: 15,
        usedDays: 5,
        remainingDays: 10,
        description: 'إجازة مرضية بشهادة طبية',
      ),
      LeaveBalance(
        leaveType: 'إجازة طارئة',
        totalDays: 5,
        usedDays: 2,
        remainingDays: 3,
        description: 'إجازة للظروف الطارئة',
      ),
    ],
    'omar.hassan': [
      LeaveBalance(
        leaveType: 'إجازة سنوية',
        totalDays: 21,
        usedDays: 5,
        remainingDays: 16,
        description: 'الإجازة السنوية المستحقة',
      ),
      LeaveBalance(
        leaveType: 'إجازة مرضية',
        totalDays: 15,
        usedDays: 1,
        remainingDays: 14,
        description: 'إجازة مرضية بشهادة طبية',
      ),
      LeaveBalance(
        leaveType: 'إجازة طارئة',
        totalDays: 3,
        usedDays: 0,
        remainingDays: 3,
        description: 'إجازة للظروف الطارئة',
      ),
    ],
  };

  static Employee? getEmployeeById(String id) {
    try {
      return _employees.firstWhere((employee) => employee.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<LeaveBalance> getLeaveBalances(String employeeId) {
    return _leaveBalances[employeeId] ?? [];
  }

  static List<LeaveRequest> getLeaveRequests(String employeeId) {
    // Mock leave requests - you can expand this
    return [
      LeaveRequest(
        id: 'REQ001',
        employeeId: employeeId,
        leaveType: 'إجازة سنوية',
        startDate: DateTime(2024, 12, 20),
        endDate: DateTime(2024, 12, 25),
        numberOfDays: 5,
        reason: 'إجازة نهاية السنة',
        status: LeaveStatus.approved,
        requestDate: DateTime(2024, 11, 15),
        approverComments: 'تمت الموافقة',
      ),
      LeaveRequest(
        id: 'REQ002',
        employeeId: employeeId,
        leaveType: 'إجازة مرضية',
        startDate: DateTime(2024, 10, 10),
        endDate: DateTime(2024, 10, 12),
        numberOfDays: 2,
        reason: 'مراجعة طبية',
        status: LeaveStatus.approved,
        requestDate: DateTime(2024, 10, 8),
        approverComments: 'تمت الموافقة مع تقديم الشهادة الطبية',
      ),
    ];
  }

  static List<String> getLeaveTypes() {
    return ['إجازة سنوية', 'إجازة مرضية', 'إجازة طارئة', 'إجازة أمومة', 'إجازة حج'];
  }
}
