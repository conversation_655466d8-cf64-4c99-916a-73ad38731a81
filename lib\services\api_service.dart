import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class ApiService {

  /// تسجيل الدخول باستخدام API
  static Future<Map<String, dynamic>> login({
    required String database,
    required String username,
    required String password,
  }) async {
    try {
      final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.loginEndpoint));
      
      final body = jsonEncode({
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {
          'db': database,
          'login': username,
          'password': password,
        },
        'id': null,
      });

      if (ApiConfig.enableDebugMode) {
        print('Sending login request to: $url');
        print('Request body: $body');
      }

      final response = await http.post(
        url,
        headers: ApiConfig.defaultHeaders,
        body: body,
      ).timeout(
        ApiConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
        },
      );

      if (ApiConfig.enableDebugMode) {
        print('Response status: ${response.statusCode}');
        print('Response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (ApiConfig.enableDebugMode) {
          print('Parsed response data: $responseData');
        }

        // التحقق من وجود خطأ في الاستجابة
        if (responseData.containsKey('error')) {
          return {
            'success': false,
            'message': responseData['error']['message'] ?? 'حدث خطأ غير معروف',
            'error_code': responseData['error']['code'],
          };
        }
        
        // التحقق من نجاح العملية
        if (responseData.containsKey('result')) {
          final outerResult = responseData['result'];

          if (ApiConfig.enableDebugMode) {
            print('Outer result: $outerResult');
          }

          // التحقق من وجود result مضاعف (nested result)
          // يمكن أن تكون الاستجابة في أحد التنسيقات التالية:
          // 1. {result: {success: true, ...}}
          // 2. {result: {result: {success: true, ...}}}
          Map<String, dynamic> result;
          if (outerResult.containsKey('result') && outerResult['result'] is Map) {
            result = outerResult['result'];
          } else {
            result = outerResult;
          }

          if (ApiConfig.enableDebugMode) {
            print('Final result: $result');
            print('Success value: ${result['success']}');
            print('Success type: ${result['success'].runtimeType}');
            print('Result contains success: ${result.containsKey('success')}');
            print('Result keys: ${result.keys.toList()}');
          }

          // التحقق من وجود حقل success والتأكد من قيمته
          if (result.containsKey('success') && result['success'] == true) {
            if (ApiConfig.enableDebugMode) {
              print('Login successful, returning success response');
              print('Session ID: ${result['session_id']}');
              print('User ID: ${result['user_id']}');
            }

            // التأكد من وجود البيانات المطلوبة
            if (result['session_id'] == null || result['user_id'] == null) {
              if (ApiConfig.enableDebugMode) {
                print('Warning: Missing session_id or user_id in response');
              }
              return {
                'success': false,
                'message': 'استجابة غير مكتملة من الخادم - مفقود session_id أو user_id',
              };
            }

            return {
              'success': true,
              'message': result['message'] ?? 'تم تسجيل الدخول بنجاح',
              'session_id': result['session_id'],
              'user_id': result['user_id'],
            };
          } else {
            if (ApiConfig.enableDebugMode) {
              print('Login failed, success is not true');
            }
            return {
              'success': false,
              'message': result['message'] ?? 'فشل في تسجيل الدخول',
            };
          }
        }
        
        return {
          'success': false,
          'message': 'استجابة غير متوقعة من الخادم',
        };
      } else {
        return {
          'success': false,
          'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
        };
      }
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('Login error: $e');
      }
      return {
        'success': false,
        'message': 'خطأ في الاتصال: ${e.toString()}',
      };
    }
  }

  /// جلب بيانات الموظف الحالي
  static Future<Map<String, dynamic>> getEmployeeData({
    required String sessionId,
  }) async {
    try {
      final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.employeeEndpoint));

      if (ApiConfig.enableDebugMode) {
        print('Sending employee data request to: $url');
        print('Session ID: $sessionId');
      }

      final response = await http.get(
        url,
        headers: {
          ...ApiConfig.defaultHeaders,
          'Cookie': 'session_id=$sessionId',
        },
      ).timeout(
        ApiConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
        },
      );

      if (ApiConfig.enableDebugMode) {
        print('Employee data response status: ${response.statusCode}');
        print('Employee data response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        // التحقق من وجود خطأ في الاستجابة
        if (responseData.containsKey('error')) {
          return {
            'success': false,
            'message': responseData['error'] ?? 'حدث خطأ في جلب بيانات الموظف',
          };
        }

        // إذا كانت الاستجابة تحتوي على البيانات مباشرة
        if (responseData.containsKey('int_id') || responseData.containsKey('name')) {
          return {
            'success': true,
            'data': responseData,
          };
        }

        return {
          'success': false,
          'message': 'لم يتم العثور على بيانات الموظف',
        };
      } else if (response.statusCode == 401) {
        return {
          'success': false,
          'message': 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.',
          'requires_login': true,
        };
      } else {
        return {
          'success': false,
          'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
        };
      }
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('Employee data error: $e');
      }
      return {
        'success': false,
        'message': 'خطأ في الاتصال: ${e.toString()}',
      };
    }
  }

  /// التحقق من صحة الاتصال بالخادم
  static Future<bool> checkServerConnection() async {
    try {
      final url = Uri.parse('${ApiConfig.baseUrl}/web/database/selector');
      final response = await http.get(url).timeout(
        const Duration(seconds: 10),
      );
      return response.statusCode == 200;
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('Server connection error: $e');
      }
      return false;
    }
  }
}
