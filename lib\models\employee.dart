class Employee {
  final String id;
  final String name;
  final String email;
  final String department;
  final String position;
  final String phoneNumber;
  final DateTime joinDate;
  final String employeeId;
  final String manager;
  final double salary;
  final String profileImage;

  Employee({
    required this.id,
    required this.name,
    required this.email,
    required this.department,
    required this.position,
    required this.phoneNumber,
    required this.joinDate,
    required this.employeeId,
    required this.manager,
    required this.salary,
    this.profileImage = '',
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      department: json['department'],
      position: json['position'],
      phoneNumber: json['phoneNumber'],
      joinDate: DateTime.parse(json['joinDate']),
      employeeId: json['employeeId'],
      manager: json['manager'],
      salary: json['salary'].toDouble(),
      profileImage: json['profileImage'] ?? '',
    );
  }

  Map<String, dynamic> toJ<PERSON>() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'department': department,
      'position': position,
      'phoneNumber': phoneNumber,
      'joinDate': joinDate.toIso8601String(),
      'employeeId': employeeId,
      'manager': manager,
      'salary': salary,
      'profileImage': profileImage,
    };
  }
}
