import 'dart:convert';

void main() {
  // محاكاة الاستجابة الفعلية من الخادم
  final responseBody = '''{"jsonrpc": "2.0", "id": null, "result": {"jsonrpc": "2.0", "result": {"success": true, "session_id": "0671da7fa18bc19cd74ef45a412e703b5c95d200", "user_id": 2, "message": "تم تسجيل الدخول بنجاح"}, "id": null}}''';
  
  print('Response body: $responseBody');
  
  final Map<String, dynamic> responseData = jsonDecode(responseBody);
  print('Parsed response data: $responseData');
  
  if (responseData.containsKey('result')) {
    final outerResult = responseData['result'];
    print('Outer result: $outerResult');
    
    // التحقق من وجود result مضاعف (nested result)
    final result = outerResult.containsKey('result') ? outerResult['result'] : outerResult;
    print('Final result: $result');
    
    print('Success value: ${result['success']}');
    print('Success type: ${result['success'].runtimeType}');
    print('Success == true: ${result['success'] == true}');
    
    if (result['success'] == true) {
      print('✅ Login should be successful!');
      print('Session ID: ${result['session_id']}');
      print('User ID: ${result['user_id']}');
      print('Message: ${result['message']}');
    } else {
      print('❌ Login failed');
    }
  } else {
    print('❌ No result found in response');
  }
}
