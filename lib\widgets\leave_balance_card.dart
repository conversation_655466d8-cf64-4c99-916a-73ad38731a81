import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/leave_balance.dart';
import '../utils/app_colors.dart';

class LeaveBalanceCard extends StatelessWidget {
  final LeaveBalance leaveBalance;

  const LeaveBalanceCard({
    super.key,
    required this.leaveBalance,
  });

  @override
  Widget build(BuildContext context) {
    final percentage = leaveBalance.totalDays > 0 
        ? (leaveBalance.remainingDays / leaveBalance.totalDays) 
        : 0.0;
    
    Color progressColor;
    IconData leaveIcon;
    
    // Determine color based on remaining percentage
    if (percentage > 0.6) {
      progressColor = AppColors.success;
    } else if (percentage > 0.3) {
      progressColor = AppColors.warning;
    } else {
      progressColor = AppColors.error;
    }

    // Determine icon based on leave type
    switch (leaveBalance.leaveType) {
      case 'إجازة سنوية':
        leaveIcon = FontAwesomeIcons.umbrellaBeach;
        break;
      case 'إجازة مرضية':
        leaveIcon = FontAwesomeIcons.userDoctor;
        break;
      case 'إجازة طارئة':
        leaveIcon = FontAwesomeIcons.triangleExclamation;
        break;
      case 'إجازة أمومة':
        leaveIcon = FontAwesomeIcons.baby;
        break;
      case 'إجازة حج':
        leaveIcon = FontAwesomeIcons.kaaba;
        break;
      default:
        leaveIcon = FontAwesomeIcons.calendar;
    }

    return Card(
      elevation: 4,
      shadowColor: progressColor.withOpacity(0.2),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: progressColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: progressColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    leaveIcon,
                    color: progressColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        leaveBalance.leaveType,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        leaveBalance.description,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: progressColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${leaveBalance.remainingDays}/${leaveBalance.totalDays}',
                    style: TextStyle(
                      color: progressColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الرصيد المتبقي',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${(percentage * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 12,
                        color: progressColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                LinearProgressIndicator(
                  value: percentage,
                  backgroundColor: AppColors.grey200,
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
                const SizedBox(height: 12),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        'المتبقي',
                        '${leaveBalance.remainingDays} يوم',
                        progressColor,
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 30,
                      color: AppColors.border,
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'المستخدم',
                        '${leaveBalance.usedDays} يوم',
                        AppColors.textSecondary,
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 30,
                      color: AppColors.border,
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'الإجمالي',
                        '${leaveBalance.totalDays} يوم',
                        AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
