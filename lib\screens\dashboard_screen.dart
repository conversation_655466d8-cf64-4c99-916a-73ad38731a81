import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../providers/auth_provider.dart';
import '../providers/employee_provider.dart';
import '../utils/app_colors.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/leave_summary_card.dart';
import '../widgets/recent_requests_card.dart';

class DashboardScreen extends StatefulWidget {
  final VoidCallback? onLogout;

  const DashboardScreen({super.key, this.onLogout});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _loadData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final employeeProvider = Provider.of<EmployeeProvider>(
      context,
      listen: false,
    );

    if (authProvider.currentUser != null) {
      employeeProvider.loadEmployeeData(authProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('لوحة التحكم'),
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, _) {
              return IconButton(
                icon: const FaIcon(FontAwesomeIcons.rightFromBracket),
                onPressed: () async {
                  await authProvider.logout();
                  widget.onLogout?.call();
                },
              );
            },
          ),
        ],
      ),
      body: Consumer2<AuthProvider, EmployeeProvider>(
        builder: (context, authProvider, employeeProvider, _) {
          final user = authProvider.currentUser;

          if (user == null) {
            return const Center(child: CircularProgressIndicator());
          }

          if (employeeProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return RefreshIndicator(
            onRefresh: () async {
              _loadData();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppColors.primary, AppColors.primaryLight],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor: AppColors.white.withOpacity(0.2),
                              child: const FaIcon(
                                FontAwesomeIcons.userTie,
                                color: AppColors.white,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'مرحباً، ${user.name}',
                                    style: const TextStyle(
                                      color: AppColors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    user.position,
                                    style: TextStyle(
                                      color: AppColors.white.withOpacity(0.9),
                                      fontSize: 14,
                                    ),
                                  ),
                                  Text(
                                    user.department,
                                    style: TextStyle(
                                      color: AppColors.white.withOpacity(0.8),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'اليوم: ${DateFormat('EEEE، d MMMM yyyy', 'ar').format(DateTime.now())}',
                          style: TextStyle(
                            color: AppColors.white.withOpacity(0.9),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Quick stats
                  Row(
                    children: [
                      Expanded(
                        child: DashboardCard(
                          title: 'الإجازات المتبقية',
                          value: '${employeeProvider.getTotalRemainingDays()}',
                          subtitle: 'يوم',
                          icon: FontAwesomeIcons.calendarCheck,
                          color: AppColors.success,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DashboardCard(
                          title: 'الإجازات المستخدمة',
                          value: '${employeeProvider.getTotalUsedDays()}',
                          subtitle: 'يوم',
                          icon: FontAwesomeIcons.calendarXmark,
                          color: AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: DashboardCard(
                          title: 'الطلبات المعلقة',
                          value:
                              '${employeeProvider.leaveRequests.where((r) => r.status.toString().contains('pending')).length}',
                          subtitle: 'طلب',
                          icon: FontAwesomeIcons.clock,
                          color: AppColors.info,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DashboardCard(
                          title: 'سنوات الخدمة',
                          value: '${DateTime.now().year - user.joinDate.year}',
                          subtitle: 'سنة',
                          icon: FontAwesomeIcons.award,
                          color: AppColors.accent,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Leave summary
                  LeaveSummaryCard(
                    leaveBalances: employeeProvider.leaveBalances,
                  ),
                  const SizedBox(height: 16),

                  // Recent requests
                  RecentRequestsCard(
                    leaveRequests: employeeProvider.leaveRequests
                        .take(3)
                        .toList(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
