import 'package:flutter/material.dart';
import '../models/leave_balance.dart';
import '../models/leave_request.dart';
import '../services/mock_data_service.dart';

class EmployeeProvider with ChangeNotifier {
  List<LeaveBalance> _leaveBalances = [];
  List<LeaveRequest> _leaveRequests = [];
  bool _isLoading = false;

  List<LeaveBalance> get leaveBalances => _leaveBalances;
  List<LeaveRequest> get leaveRequests => _leaveRequests;
  bool get isLoading => _isLoading;

  Future<void> loadEmployeeData(String employeeId) async {
    _isLoading = true;
    notifyListeners();

    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 500));

    _leaveBalances = MockDataService.getLeaveBalances(employeeId);
    _leaveRequests = MockDataService.getLeaveRequests(employeeId);

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> submitLeaveRequest({
    required String employeeId,
    required String leaveType,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
  }) async {
    _isLoading = true;
    notifyListeners();

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    // Calculate number of days
    final numberOfDays = endDate.difference(startDate).inDays + 1;

    // Create new leave request
    final newRequest = LeaveRequest(
      id: 'REQ${DateTime.now().millisecondsSinceEpoch}',
      employeeId: employeeId,
      leaveType: leaveType,
      startDate: startDate,
      endDate: endDate,
      numberOfDays: numberOfDays,
      reason: reason,
      status: LeaveStatus.pending,
      requestDate: DateTime.now(),
    );

    // Add to the list
    _leaveRequests.insert(0, newRequest);

    _isLoading = false;
    notifyListeners();
    
    return true; // Simulate successful submission
  }

  List<String> getAvailableLeaveTypes() {
    return MockDataService.getLeaveTypes();
  }

  int getTotalRemainingDays() {
    return _leaveBalances.fold(0, (sum, balance) => sum + balance.remainingDays);
  }

  int getTotalUsedDays() {
    return _leaveBalances.fold(0, (sum, balance) => sum + balance.usedDays);
  }
}
